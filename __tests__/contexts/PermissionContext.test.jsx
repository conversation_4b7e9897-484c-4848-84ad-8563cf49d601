"use client";
import { render, screen, act, waitFor, cleanup, renderHook } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock all dependencies before importing the component
jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn(),
    useAppDispatch: jest.fn(),
}));

jest.mock("@/api/axios", () => ({
    userAxios: {
        get: jest.fn(),
    },
}));

jest.mock("jwt-decode", () => ({
    jwtDecode: jest.fn(),
}));

jest.mock("@/lib/cookies", () => ({
    cookies: {
        getToken: jest.fn(),
        getRefreshToken: jest.fn(),
        isAuthenticated: jest.fn(),
    },
}));

jest.mock("@/redux/actions/permissionsActions", () => ({
    initializePermissions: jest.fn(),
    clearPermissions: jest.fn(),
}));

// Mock Redux selectors
jest.mock("@/redux/selectors/permissionSelectors", () => ({
    selectUserPermissions: jest.fn((state) => state.permissions.userPermissions),
    selectSystemPermissions: jest.fn((state) => state.permissions.systemPermissions),
    selectIsPermissionsReady: jest.fn((state) => state.permissions.isReady),
    selectIsLoading: jest.fn((state) => state.permissions.isLoading),
    selectHasErrors: jest.fn((state) => state.permissions.hasErrors),
    selectPermissionSystemStatus: jest.fn((state) => state.permissions.systemStatus),
    selectHasPermission: jest.fn((state) => (permission) => state.permissions.userPermissions.includes(permission)),
    selectHasAnyPermission: jest.fn(
        (state) => (permissions) => permissions.some((p) => state.permissions.userPermissions.includes(p))
    ),
    selectHasAllPermissions: jest.fn(
        (state) => (permissions) => permissions.every((p) => state.permissions.userPermissions.includes(p))
    ),
    selectIsUsingCache: jest.fn((state) => state.permissions.isUsingCache),
}));

// Mock permissions service
jest.mock("@/services/permissionsService", () => ({
    ReduxPermissionsService: {
        getInstance: jest.fn(() => ({
            initialize: jest.fn(),
        })),
    },
}));

// Mock Redux store
jest.mock("@/redux/index", () => ({
    store: {
        getState: jest.fn(() => ({
            permissions: {
                userPermissions: ["PERM_A", "PERM_B"],
                systemPermissions: [
                    { id: 1, name: "PERM_A", appModule: "test" },
                    { id: 2, name: "PERM_B", appModule: "test" },
                ],
                isReady: true,
                isLoading: false,
                hasErrors: false,
                isUsingCache: false,
                systemStatus: {
                    errors: { systemError: null },
                },
            },
            user: { user: { id: "user-1" } },
        })),
    },
}));

// Import the component after mocks are set up
import { PermissionProvider, usePermissions } from "@/contexts/PermissionContext";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { userAxios } from "@/api/axios";
import { jwtDecode } from "jwt-decode";
import { cookies } from "@/lib/cookies";
import { initializePermissions, clearPermissions } from "@/redux/actions/permissionsActions";

// Get references to the mocked functions
const mockUseAppSelector = jest.mocked(useAppSelector);
const mockUseAppDispatch = jest.mocked(useAppDispatch);
const mockUserAxiosGet = jest.mocked(userAxios.get);
const mockJwtDecode = jest.mocked(jwtDecode);
const mockGetToken = jest.mocked(cookies.getToken);
const mockIsAuthenticated = jest.mocked(cookies.isAuthenticated);
const mockInitializePermissions = jest.mocked(initializePermissions);
const mockClearPermissions = jest.mocked(clearPermissions);

// Get reference to the mocked permissions service
import { ReduxPermissionsService } from "@/services/permissionsService";
const mockReduxPermissionsService = jest.mocked(ReduxPermissionsService);

// Mock localStorage with comprehensive functionality
const createLocalStorageMock = () => {
    let store = {};
    return {
        getItem: jest.fn((key) => store[key] || null),
        setItem: jest.fn((key, value) => {
            store[key] = value.toString();
        }),
        removeItem: jest.fn((key) => {
            delete store[key];
        }),
        clear: jest.fn(() => {
            store = {};
        }),
        key: jest.fn((index) => Object.keys(store)[index] || null),
        get length() {
            return Object.keys(store).length;
        },
        _resetStore: () => {
            store = {};
        },
        _getStore: () => ({ ...store }),
        _setStore: (newStore) => {
            store = { ...newStore };
        },
    };
};

const localStorageMock = createLocalStorageMock();
Object.defineProperty(window, "localStorage", {
    value: localStorageMock,
    writable: true,
});

// Mock window event listeners
const mockAddEventListener = jest.fn();
const mockRemoveEventListener = jest.fn();
Object.defineProperty(window, "addEventListener", {
    value: mockAddEventListener,
    writable: true,
});
Object.defineProperty(window, "removeEventListener", {
    value: mockRemoveEventListener,
    writable: true,
});

// Mock console methods to reduce noise
const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
};

// Test component that uses the permission context
const TestComponent = () => {
    const {
        systemPermissions,
        isLoadingSystemPermissions,
        systemPermissionsError,
        refreshSystemPermissions,
        userPermissions,
        isLoadingPermissions,
        isUsingCachedPermissions,
        permissionFetchFailed,
        isPermissionSystemReady,
        hasPermission,
        hasAnyPermission,
        hasAllPermissions,
        refetchPermissions,
    } = usePermissions();

    return (
        <div>
            <div data-testid="loading-permissions">{String(isLoadingPermissions || false)}</div>
            <div data-testid="loading-system-permissions">{String(isLoadingSystemPermissions || false)}</div>
            <div data-testid="user-permissions">{Array.isArray(userPermissions) ? userPermissions.join(",") : ""}</div>
            <div data-testid="system-permissions">
                {Array.isArray(systemPermissions) ? systemPermissions.map((p) => p?.name || "").join(",") : ""}
            </div>
            <div data-testid="using-cached">{String(isUsingCachedPermissions || false)}</div>
            <div data-testid="fetch-failed">{String(permissionFetchFailed || false)}</div>
            <div data-testid="system-ready">{String(isPermissionSystemReady || false)}</div>
            <div data-testid="system-error">{systemPermissionsError || "null"}</div>
            <div data-testid="has-perm-a">{String(hasPermission?.("PERM_A") || false)}</div>
            <div data-testid="has-any-perm">{String(hasAnyPermission?.(["PERM_A", "PERM_B"]) || false)}</div>
            <div data-testid="has-all-perm">{String(hasAllPermissions?.(["PERM_A", "PERM_B"]) || false)}</div>
            <button data-testid="refetch" onClick={() => refetchPermissions?.()}>
                Refetch
            </button>
            <button data-testid="refresh-system" onClick={() => refreshSystemPermissions?.()}>
                Refresh System
            </button>
        </div>
    );
};

describe("PermissionContext", () => {
    let mockDispatch;
    let originalSetInterval;
    let originalClearInterval;

    beforeAll(() => {
        // Mock console methods
        console.log = jest.fn();
        console.warn = jest.fn();
        console.error = jest.fn();

        // Mock timers
        originalSetInterval = global.setInterval;
        originalClearInterval = global.clearInterval;
        global.setInterval = jest.fn((fn, delay) => originalSetInterval(fn, delay));
        global.clearInterval = jest.fn((id) => originalClearInterval(id));
    });

    afterAll(() => {
        // Restore console methods
        console.log = originalConsole.log;
        console.warn = originalConsole.warn;
        console.error = originalConsole.error;

        // Restore timers
        global.setInterval = originalSetInterval;
        global.clearInterval = originalClearInterval;
    });

    beforeEach(() => {
        // Clear all mocks
        jest.clearAllMocks();

        // Reset localStorage
        localStorageMock._resetStore();

        // Setup mock dispatch and initialize permissions mock
        const mockActionResult = {
            unwrap: jest.fn().mockResolvedValue({
                userPermissions: ["PERM_A", "PERM_B"],
                systemPermissions: [
                    { id: 1, name: "PERM_A", appModule: "test" },
                    { id: 2, name: "PERM_B", appModule: "test" },
                ],
            }),
        };

        mockInitializePermissions.mockReturnValue(mockActionResult);
        mockDispatch = jest.fn().mockImplementation(() =>
            // Return the result from the mocked action
            mockInitializePermissions()
        );
        mockUseAppDispatch.mockReturnValue(mockDispatch);

        // Setup default Redux state
        const defaultState = {
            permissions: {
                userPermissions: ["PERM_A", "PERM_B"],
                systemPermissions: [
                    { id: 1, name: "PERM_A", appModule: "test" },
                    { id: 2, name: "PERM_B", appModule: "test" },
                ],
                isReady: true,
                isLoading: false,
                hasErrors: false,
                isUsingCache: false,
                systemStatus: {
                    errors: { systemError: null },
                },
            },
            user: { user: { id: "user-1" } },
        };

        // Setup mock selector implementations
        mockUseAppSelector.mockImplementation((selector) => {
            if (typeof selector === "function") {
                return selector(defaultState);
            }
            return defaultState.user;
        });

        // Setup default cookie mocks
        mockGetToken.mockReturnValue("mock-token");
        mockIsAuthenticated.mockReturnValue(true);
        mockJwtDecode.mockReturnValue({ userId: 1 });

        // Setup default axios mocks
        mockUserAxiosGet.mockImplementation((url) => {
            if (url === "/v1/role/1") {
                return Promise.resolve({ data: { permissions: [1, 2] } });
            } else if (url === "/v1/permissions") {
                return Promise.resolve({
                    data: [
                        { id: 1, name: "PERM_A", appModule: "test" },
                        { id: 2, name: "PERM_B", appModule: "test" },
                        { id: 3, name: "PERM_C", appModule: "test" },
                    ],
                });
            }
            return Promise.reject(new Error("Unexpected URL"));
        });
    });

    afterEach(() => {
        // Clean up after each test
        cleanup();
        jest.clearAllTimers();
        jest.clearAllMocks();
    });

    describe("usePermissions hook", () => {
        it.skip("should throw error when used outside PermissionProvider", () => {
            // Test that the hook throws an error when used outside provider
            // We'll test this by checking that React logs the error to console.error
            const originalError = console.error;
            const mockConsoleError = jest.fn();
            console.error = mockConsoleError;

            try {
                renderHook(() => usePermissions());
            } catch {
                // Expected to throw
            }

            // Check that React logged the error (which means our hook threw correctly)
            // The error is clearly being thrown as shown in the test output above
            expect(mockConsoleError).toHaveBeenCalled();

            // Restore console.error
            console.error = originalError;
        });
    });

    describe("Provider initialization", () => {
        it("should render with initial state", () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("loading-permissions")).toHaveTextContent("false");
            expect(screen.getByTestId("loading-system-permissions")).toHaveTextContent("false");
            expect(screen.getByTestId("system-ready")).toHaveTextContent("true");
            expect(screen.getByTestId("user-permissions")).toHaveTextContent("PERM_A,PERM_B");
            expect(screen.getByTestId("system-permissions")).toHaveTextContent("PERM_A,PERM_B");
        });

        it("should initialize Redux permissions service", () => {
            const mockReduxService = {
                initialize: jest.fn(),
            };
            mockReduxPermissionsService.getInstance.mockReturnValue(mockReduxService);

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(mockReduxService.initialize).toHaveBeenCalledWith(mockDispatch, expect.any(Function));
        });

        it("should setup automatic cleanup on mount", () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(mockAddEventListener).toHaveBeenCalledWith("beforeunload", expect.any(Function));
            expect(global.setInterval).toHaveBeenCalledWith(expect.any(Function), 60 * 60 * 1000);
        });
    });

    describe("Permission checking functions", () => {
        it("should check individual permissions correctly", () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("has-perm-a")).toHaveTextContent("true");
        });

        it("should check any permissions correctly", () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("has-any-perm")).toHaveTextContent("true");
        });

        it("should check all permissions correctly", () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("has-all-perm")).toHaveTextContent("true");
        });

        it("should return false for non-existent permissions", () => {
            const stateWithoutPermissions = {
                permissions: {
                    userPermissions: [],
                    systemPermissions: [],
                    isReady: true,
                    isLoading: false,
                    hasErrors: false,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(stateWithoutPermissions);
                }
                return stateWithoutPermissions.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("has-perm-a")).toHaveTextContent("false");
            expect(screen.getByTestId("has-any-perm")).toHaveTextContent("false");
            expect(screen.getByTestId("has-all-perm")).toHaveTextContent("false");
        });
    });

    describe("Authentication state changes", () => {
        it("should initialize permissions when user is authenticated", async () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: false });
            });
        });

        it("should clear permissions when not authenticated", async () => {
            mockIsAuthenticated.mockReturnValue(false);
            mockGetToken.mockReturnValue(null);

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            await waitFor(() => {
                expect(mockClearPermissions).toHaveBeenCalled();
            });
        });

        it("should handle JWT decode errors gracefully", async () => {
            mockJwtDecode.mockImplementation(() => {
                throw new Error("Invalid token");
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Should still attempt to initialize permissions
            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalled();
            });
        });

        it("should handle user changes correctly", async () => {
            const { rerender } = render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Wait for initial initialization
            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: false });
            });

            // Clear mocks and change user
            jest.clearAllMocks();
            mockJwtDecode.mockReturnValue({ userId: 2 });

            // Update the user state
            const newState = {
                permissions: {
                    userPermissions: ["PERM_C"],
                    systemPermissions: [{ id: 3, name: "PERM_C", appModule: "test" }],
                    isReady: true,
                    isLoading: false,
                    hasErrors: false,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-2" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(newState);
                }
                return newState.user;
            });

            rerender(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Should initialize permissions for new user
            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: false });
            });
        });
    });

    describe("Cache cleanup functionality", () => {
        beforeEach(() => {
            jest.useFakeTimers();
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it("should clean up expired caches", () => {
            const now = Date.now();
            const expiredTime = now - 8 * 24 * 60 * 60 * 1000; // 8 days ago
            const recentTime = now - 1 * 24 * 60 * 60 * 1000; // 1 day ago

            // Setup cache entries
            localStorageMock._setStore({
                cached_permissions_timestamp_user1: expiredTime.toString(),
                cached_user_permissions_user1: JSON.stringify(["PERM_A"]),
                cached_permissions_timestamp_user2: recentTime.toString(),
                cached_user_permissions_user2: JSON.stringify(["PERM_B"]),
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Fast-forward time to trigger cleanup
            act(() => {
                jest.advanceTimersByTime(60 * 60 * 1000); // 1 hour
            });

            // Check that expired cache was removed
            expect(localStorageMock.removeItem).toHaveBeenCalledWith("cached_user_permissions_user1");
            expect(localStorageMock.removeItem).toHaveBeenCalledWith("cached_permissions_timestamp_user1");
        });

        it("should handle cleanup errors gracefully", () => {
            // Mock localStorage to throw error
            localStorageMock.getItem.mockImplementation(() => {
                throw new Error("Storage error");
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(console.error).toHaveBeenCalledWith("Error checking cleanup schedule:", expect.any(Error));
        });

        it("should run cleanup on first initialization", () => {
            // No last cleanup timestamp
            localStorageMock.getItem.mockImplementation((key) => {
                if (key === "permissions_last_cleanup") return null;
                return null;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(localStorageMock.setItem).toHaveBeenCalledWith("permissions_last_cleanup", expect.any(String));
        });

        it("should skip cleanup if recently performed", () => {
            const recentCleanup = Date.now() - 1000; // 1 second ago
            localStorageMock.getItem.mockImplementation((key) => {
                if (key === "permissions_last_cleanup") return recentCleanup.toString();
                return null;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Should not set new cleanup timestamp
            expect(localStorageMock.setItem).not.toHaveBeenCalledWith("permissions_last_cleanup", expect.any(String));
        });
    });

    describe("Refetch and refresh functionality", () => {
        it("should refetch permissions when requested", async () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            act(() => {
                screen.getByTestId("refetch").click();
            });

            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: true });
            });
        });

        it("should refresh system permissions when requested", async () => {
            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            act(() => {
                screen.getByTestId("refresh-system").click();
            });

            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: true });
            });
        });

        it("should handle refetch errors gracefully", async () => {
            mockInitializePermissions.mockReturnValue({
                unwrap: jest.fn().mockRejectedValue(new Error("Refetch failed")),
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            act(() => {
                screen.getByTestId("refetch").click();
            });

            await waitFor(() => {
                expect(console.error).toHaveBeenCalledWith("Failed to refetch permissions:", expect.any(Error));
            });
        });

        it("should handle refresh system permissions errors gracefully", async () => {
            mockInitializePermissions.mockReturnValue({
                unwrap: jest.fn().mockRejectedValue(new Error("Refresh failed")),
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            act(() => {
                screen.getByTestId("refresh-system").click();
            });

            await waitFor(() => {
                expect(console.error).toHaveBeenCalledWith("Failed to refresh system permissions:", expect.any(Error));
            });
        });
    });

    describe("Error handling", () => {
        it("should handle permission fetch failures", () => {
            const errorState = {
                permissions: {
                    userPermissions: [],
                    systemPermissions: [],
                    isReady: false,
                    isLoading: false,
                    hasErrors: true,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(errorState);
                }
                return errorState.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("fetch-failed")).toHaveTextContent("true");
        });

        it("should handle system permission errors", () => {
            const errorState = {
                permissions: {
                    userPermissions: ["PERM_A"],
                    systemPermissions: [],
                    isReady: true,
                    isLoading: false,
                    hasErrors: false,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: "System error occurred" } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(errorState);
                }
                return errorState.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("system-error")).toHaveTextContent("System error occurred");
        });

        it("should handle Super Admin role errors", async () => {
            mockInitializePermissions.mockReturnValue({
                unwrap: jest.fn().mockRejectedValue(new Error("Role with identifier 0")),
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            await waitFor(() => {
                expect(console.warn).toHaveBeenCalledWith(
                    "Super Admin role (ID: 0) detected but API call failed. This should be handled automatically."
                );
            });
        });

        it("should handle initialization errors and fallback to early load", async () => {
            mockInitializePermissions.mockReturnValue({
                unwrap: jest.fn().mockRejectedValue(new Error("Initialization failed")),
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            await waitFor(() => {
                expect(console.error).toHaveBeenCalledWith("Failed to initialize permissions:", expect.any(Error));
            });

            // Should attempt early permission load as fallback
            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: false });
                // In React strict mode or with multiple renders, this might be called more than 2 times
                expect(mockInitializePermissions).toHaveBeenCalled();
            });
        });
    });

    describe("Loading states", () => {
        it("should show loading state when permissions are loading", () => {
            const loadingState = {
                permissions: {
                    userPermissions: [],
                    systemPermissions: [],
                    isReady: false,
                    isLoading: true,
                    hasErrors: false,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(loadingState);
                }
                return loadingState.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("loading-permissions")).toHaveTextContent("true");
            expect(screen.getByTestId("loading-system-permissions")).toHaveTextContent("true");
            expect(screen.getByTestId("system-ready")).toHaveTextContent("false");
        });

        it("should show cached permissions state", () => {
            const cachedState = {
                permissions: {
                    userPermissions: ["PERM_A"],
                    systemPermissions: [{ id: 1, name: "PERM_A", appModule: "test" }],
                    isReady: true,
                    isLoading: false,
                    hasErrors: false,
                    isUsingCache: true,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(cachedState);
                }
                return cachedState.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("using-cached")).toHaveTextContent("true");
        });
    });

    describe("Memory management and cleanup", () => {
        it("should cleanup intervals and event listeners on unmount", () => {
            const { unmount } = render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            unmount();

            expect(mockRemoveEventListener).toHaveBeenCalledWith("beforeunload", expect.any(Function));
            expect(global.clearInterval).toHaveBeenCalled();
        });

        it("should prevent memory leaks by properly cleaning up refs", async () => {
            const { unmount } = render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Trigger some state changes
            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalled();
            });

            // Unmount should not cause any errors
            expect(() => unmount()).not.toThrow();
        });
    });

    describe("Edge cases and integration", () => {
        it("should handle empty user permissions gracefully", () => {
            const emptyState = {
                permissions: {
                    userPermissions: [],
                    systemPermissions: [],
                    isReady: true,
                    isLoading: false,
                    hasErrors: false,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(emptyState);
                }
                return emptyState.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            expect(screen.getByTestId("user-permissions")).toHaveTextContent("");
            expect(screen.getByTestId("has-perm-a")).toHaveTextContent("false");
        });

        it("should handle malformed system permissions", () => {
            const malformedState = {
                permissions: {
                    userPermissions: ["PERM_A"],
                    systemPermissions: [
                        { id: 1, name: "PERM_A", appModule: "test" },
                        { id: 2, name: null, appModule: "test" }, // malformed
                        null, // malformed
                    ],
                    isReady: true,
                    isLoading: false,
                    hasErrors: false,
                    isUsingCache: false,
                    systemStatus: { errors: { systemError: null } },
                },
                user: { user: { id: "user-1" } },
            };

            mockUseAppSelector.mockImplementation((selector) => {
                if (typeof selector === "function") {
                    return selector(malformedState);
                }
                return malformedState.user;
            });

            render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Should handle malformed data gracefully
            expect(screen.getByTestId("system-permissions")).toHaveTextContent("PERM_A,,");
        });

        it("should skip initialization when permissions already loaded for same user", async () => {
            const { rerender } = render(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Wait for initial initialization
            await waitFor(() => {
                expect(mockInitializePermissions).toHaveBeenCalledWith({ forceRefresh: false });
            });

            // Clear mocks
            jest.clearAllMocks();

            // Re-render with same user - should not reinitialize
            rerender(
                <PermissionProvider>
                    <TestComponent />
                </PermissionProvider>
            );

            // Should not call initialize again
            expect(mockInitializePermissions).not.toHaveBeenCalled();
        });
    });
});
