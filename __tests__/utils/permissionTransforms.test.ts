/**
 * @file permissionTransforms.test.ts
 * Test suite for permission transformation utilities
 */

import {
    transformToBackwardCompatibleFormat,
    normalizePermissionName,
    normalizeModuleName,
    groupPermissionsByModule,
    createEmptyPermissionsStructure,
    createPermissionLookup,
    checkPermissionWithFallback,
    MODULE_MAPPING,
} from "@/utils/permissionTransforms";

describe("permissionTransforms", () => {
    describe("createEmptyPermissionsStructure", () => {
        it("should create empty permissions structure", () => {
            const result = createEmptyPermissionsStructure();

            expect(result).toEqual({
                OVERVIEW_PERMISSIONS: {},
                SETTINGS_PERMISSIONS: {},
                OUTGOING_PAYMENTS_PERMISSIONS: {},
                SEND_MONEY_PERMISSIONS: {},
                ACCOUNTS_PERMISSIONS: {},
                TRANSACTIONS_PERMISSIONS: {},
                BILL_PAYMENTS_PERMISSIONS: {},
                BENEFICIARIES_PERMISSIONS: {},
                TEAM_MANAGEMENT_PERMISSIONS: {},
                SUPPORT_PERMISSIONS: {},
                ONBOARDING_PERMISSIONS: {},
                ALL_PERMISSIONS: {},
            });
        });
    });

    describe("transformToBackwardCompatibleFormat", () => {
        it("should transform permissions to backward compatible format", () => {
            const mockAllPermissions = {
                DASHBOARD_PERMISSIONS: {
                    VIEW_DASHBOARD: "View Dashboard",
                    MANAGE_DASHBOARD: "Manage Dashboard",
                },
                USER_MANAGEMENT_PERMISSIONS: {
                    MANAGE_USERS: "Manage Users",
                    VIEW_USERS: "View Users",
                },
            };

            const result = transformToBackwardCompatibleFormat(mockAllPermissions);

            expect(result).toHaveProperty("OVERVIEW_PERMISSIONS");
            expect(result).toHaveProperty("SETTINGS_PERMISSIONS");
            expect(result).toHaveProperty("ALL_PERMISSIONS");

            // Check that permissions are mapped correctly
            expect(result.OVERVIEW_PERMISSIONS).toEqual({
                VIEW_DASHBOARD: "View Dashboard",
                MANAGE_DASHBOARD: "Manage Dashboard",
            });
            expect(result.SETTINGS_PERMISSIONS).toEqual({
                MANAGE_USERS: "Manage Users",
                VIEW_USERS: "View Users",
            });
        });

        it("should handle empty permissions object", () => {
            const result = transformToBackwardCompatibleFormat({});

            expect(result).toHaveProperty("OVERVIEW_PERMISSIONS", {});
            expect(result).toHaveProperty("SETTINGS_PERMISSIONS", {});
            expect(result).toHaveProperty("ALL_PERMISSIONS", {});
        });
    });

    describe("normalizePermissionName", () => {
        it("should normalize permission names correctly", () => {
            expect(normalizePermissionName("View Dashboard")).toBe("VIEW_DASHBOARD");
            expect(normalizePermissionName("Manage Users & Roles")).toBe("MANAGE_USERS_ROLES");
            expect(normalizePermissionName("Create-New-Transaction")).toBe("CREATE_NEW_TRANSACTION");
            expect(normalizePermissionName("  Extra   Spaces  ")).toBe("EXTRA_SPACES");
        });

        it("should handle empty strings", () => {
            expect(normalizePermissionName("")).toBe("");
            expect(normalizePermissionName("   ")).toBe("");
        });

        it("should handle special characters and numbers", () => {
            expect(normalizePermissionName("Permission123")).toBe("PERMISSION123");
            expect(normalizePermissionName("Test@Permission#1")).toBe("TEST_PERMISSION_1");
            expect(normalizePermissionName("Multi---Dash")).toBe("MULTI_DASH");
        });
    });

    describe("normalizeModuleName", () => {
        it("should normalize module names correctly", () => {
            expect(normalizeModuleName("Dashboard")).toBe("DASHBOARD_PERMISSIONS");
            expect(normalizeModuleName("User Management")).toBe("USER_MANAGEMENT_PERMISSIONS");
            expect(normalizeModuleName("Bill-Payments")).toBe("BILL_PAYMENTS_PERMISSIONS");
        });

        it("should handle modules that already end with _PERMISSIONS", () => {
            expect(normalizeModuleName("DASHBOARD_PERMISSIONS")).toBe("DASHBOARD_PERMISSIONS");
            expect(normalizeModuleName("User_Management_Permissions")).toBe("USER_MANAGEMENT_PERMISSIONS");
        });

        it("should handle empty strings", () => {
            expect(normalizeModuleName("")).toBe("_PERMISSIONS");
            expect(normalizeModuleName("   ")).toBe("_PERMISSIONS");
        });
    });

    describe("groupPermissionsByModule", () => {
        it("should group permissions by module", () => {
            const permissions = [
                { name: "View Dashboard", appModule: "Dashboard" },
                { name: "Manage Users", appModule: "User Management" },
                { name: "Create Transaction", appModule: "Transactions" },
            ];

            const result = groupPermissionsByModule(permissions);

            expect(result).toHaveProperty("DASHBOARD_PERMISSIONS");
            expect(result).toHaveProperty("USER_MANAGEMENT_PERMISSIONS");
            expect(result).toHaveProperty("TRANSACTIONS_PERMISSIONS");

            expect(result.DASHBOARD_PERMISSIONS).toEqual({
                VIEW_DASHBOARD: "View Dashboard",
            });
            expect(result.USER_MANAGEMENT_PERMISSIONS).toEqual({
                MANAGE_USERS: "Manage Users",
            });
            expect(result.TRANSACTIONS_PERMISSIONS).toEqual({
                CREATE_TRANSACTION: "Create Transaction",
            });
        });

        it("should handle permissions with null or undefined appModule", () => {
            const permissions = [
                { name: "Generic Permission", appModule: null as any },
                { name: "Another Permission", appModule: undefined as any },
            ];

            const result = groupPermissionsByModule(permissions);

            expect(result).toHaveProperty("GENERAL_PERMISSIONS");
            expect(result.GENERAL_PERMISSIONS).toEqual({
                GENERIC_PERMISSION: "Generic Permission",
                ANOTHER_PERMISSION: "Another Permission",
            });
        });
    });

    describe("normalizePermissionName", () => {
        it("should normalize permission names correctly", () => {
            expect(normalizePermissionName("View Dashboard")).toBe("VIEW_DASHBOARD");
            expect(normalizePermissionName("Manage Users & Roles")).toBe("MANAGE_USERS_ROLES");
            expect(normalizePermissionName("Create-New-Transaction")).toBe("CREATE_NEW_TRANSACTION");
            expect(normalizePermissionName("  Extra   Spaces  ")).toBe("EXTRA_SPACES");
        });

        it("should handle empty or null strings", () => {
            expect(normalizePermissionName("")).toBe("");
            expect(normalizePermissionName("   ")).toBe("");
            expect(normalizePermissionName(null as any)).toBe("");
            expect(normalizePermissionName(undefined as any)).toBe("");
        });

        it("should handle special characters and numbers", () => {
            expect(normalizePermissionName("Permission123")).toBe("PERMISSION123");
            expect(normalizePermissionName("Test@Permission#1")).toBe("TEST_PERMISSION_1");
            expect(normalizePermissionName("Multi---Dash")).toBe("MULTI_DASH");
        });

        it("should handle unicode characters", () => {
            expect(normalizePermissionName("Café Management")).toBe("CAF_MANAGEMENT");
            expect(normalizePermissionName("Naïve Permission")).toBe("NAV_PERMISSION");
        });
    });

    describe("groupPermissionsByModule", () => {
        it("should group permissions by module correctly", () => {
            const result = groupPermissionsByModule(mockPermissions);

            expect(result).toHaveProperty("Dashboard");
            expect(result).toHaveProperty("User Management");
            expect(result).toHaveProperty("Transactions");

            expect(result.Dashboard).toHaveLength(1);
            expect(result["User Management"]).toHaveLength(1);
            expect(result.Transactions).toHaveLength(2);

            expect(result.Dashboard[0].name).toBe("View Dashboard");
            expect(result.Transactions).toContainEqual(expect.objectContaining({ name: "View Transactions" }));
            expect(result.Transactions).toContainEqual(expect.objectContaining({ name: "Create Transaction" }));
        });

        it("should handle empty permissions array", () => {
            const result = groupPermissionsByModule([]);
            expect(result).toEqual({});
        });

        it("should handle permissions with null appModule", () => {
            const permissionsWithNull: Permission[] = [
                {
                    ...mockPermissions[0],
                    appModule: null as any,
                },
            ];

            const result = groupPermissionsByModule(permissionsWithNull);

            expect(result).toHaveProperty("General");
            expect(result.General).toHaveLength(1);
        });
    });

    describe("createPermissionKey", () => {
        it("should create permission keys correctly", () => {
            expect(createPermissionKey("Dashboard", "View Dashboard")).toBe("DASHBOARD_PERMISSIONS.VIEW_DASHBOARD");
            expect(createPermissionKey("User Management", "Manage Users")).toBe(
                "USER_MANAGEMENT_PERMISSIONS.MANAGE_USERS"
            );
        });

        it("should handle special characters in module and permission names", () => {
            expect(createPermissionKey("User & Role Management", "Create-New User")).toBe(
                "USER_ROLE_MANAGEMENT_PERMISSIONS.CREATE_NEW_USER"
            );
        });

        it("should handle empty or null inputs", () => {
            expect(createPermissionKey("", "Test")).toBe("_PERMISSIONS.TEST");
            expect(createPermissionKey("Module", "")).toBe("MODULE_PERMISSIONS.");
            expect(createPermissionKey(null as any, "Test")).toBe("_PERMISSIONS.TEST");
        });
    });

    describe("validatePermissionData", () => {
        it("should validate correct permission data", () => {
            expect(validatePermissionData(mockPermissions[0])).toBe(true);
        });

        it("should reject invalid permission data", () => {
            expect(validatePermissionData(null as any)).toBe(false);
            expect(validatePermissionData(undefined as any)).toBe(false);
            expect(validatePermissionData({} as any)).toBe(false);
            expect(validatePermissionData({ id: 1 } as any)).toBe(false);
            expect(validatePermissionData({ id: 1, name: "" } as any)).toBe(false);
        });

        it("should handle edge cases in validation", () => {
            const validPermission = {
                id: 1,
                name: "Test Permission",
                appModule: "Test Module",
                endpoint: "/test",
                method: "GET",
                createdDate: null,
                createdBy: null,
                lastModifiedDate: null,
                lastModifiedBy: null,
            };

            expect(validatePermissionData(validPermission)).toBe(true);

            // Test with missing optional fields
            const minimalPermission = {
                id: 1,
                name: "Test Permission",
                appModule: "Test Module",
                endpoint: "/test",
                method: "GET",
            };

            expect(validatePermissionData(minimalPermission as any)).toBe(true);
        });
    });

    describe("transformPermissionsList", () => {
        it("should transform permissions list correctly", () => {
            const result = transformPermissionsList(mockPermissions);

            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(mockPermissions.length);
            expect(result[0]).toHaveProperty("normalizedName");
            expect(result[0]).toHaveProperty("moduleKey");
            expect(result[0].normalizedName).toBe("VIEW_DASHBOARD");
            expect(result[0].moduleKey).toBe("DASHBOARD_PERMISSIONS");
        });

        it("should handle empty array", () => {
            const result = transformPermissionsList([]);
            expect(result).toEqual([]);
        });

        it("should filter out invalid permissions", () => {
            const mixedPermissions = [
                mockPermissions[0],
                null as any,
                mockPermissions[1],
                { id: "invalid" } as any,
                mockPermissions[2],
            ];

            const result = transformPermissionsList(mixedPermissions);

            expect(result).toHaveLength(3); // Only valid permissions
            expect(result.every((p) => p.normalizedName && p.moduleKey)).toBe(true);
        });
    });

    describe("getModuleFromPermissionName", () => {
        it("should extract module from permission name", () => {
            expect(getModuleFromPermissionName("Dashboard.View")).toBe("Dashboard");
            expect(getModuleFromPermissionName("User Management.Create User")).toBe("User Management");
            expect(getModuleFromPermissionName("Simple Permission")).toBe("General");
        });

        it("should handle edge cases", () => {
            expect(getModuleFromPermissionName("")).toBe("General");
            expect(getModuleFromPermissionName(".")).toBe("General");
            expect(getModuleFromPermissionName("Multiple.Dots.Here")).toBe("Multiple");
        });
    });

    describe("sanitizeModuleName", () => {
        it("should sanitize module names correctly", () => {
            expect(sanitizeModuleName("User Management")).toBe("USER_MANAGEMENT");
            expect(sanitizeModuleName("Dashboard & Analytics")).toBe("DASHBOARD_ANALYTICS");
            expect(sanitizeModuleName("Multi---Word---Module")).toBe("MULTI_WORD_MODULE");
        });

        it("should handle special cases", () => {
            expect(sanitizeModuleName("")).toBe("GENERAL");
            expect(sanitizeModuleName("   ")).toBe("GENERAL");
            expect(sanitizeModuleName(null as any)).toBe("GENERAL");
            expect(sanitizeModuleName(undefined as any)).toBe("GENERAL");
        });
    });

    describe("Performance and edge cases", () => {
        it("should handle large datasets efficiently", () => {
            const largeDataset: Permission[] = Array.from({ length: 1000 }, (_, i) => ({
                id: i + 1,
                name: `Permission ${i + 1}`,
                appModule: `Module ${Math.floor(i / 10) + 1}`,
                endpoint: `/endpoint${i}`,
                method: "GET",
                createdDate: null,
                createdBy: null,
                lastModifiedDate: null,
                lastModifiedBy: null,
            }));

            const startTime = performance.now();
            const result = transformToBackwardCompatibleFormat(largeDataset);
            const endTime = performance.now();

            expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
            expect(Object.keys(result)).toHaveLength(100); // 100 modules
        });

        it("should handle malformed data gracefully", () => {
            const malformedData = [
                { id: 1, name: "Valid Permission", appModule: "Valid Module" },
                { id: "string-id", name: 123, appModule: [] },
                { name: "Missing ID", appModule: "Test" },
                null,
                undefined,
                "string-instead-of-object",
            ] as any;

            expect(() => {
                transformToBackwardCompatibleFormat(malformedData);
            }).not.toThrow();

            const result = transformToBackwardCompatibleFormat(malformedData);
            expect(result).toHaveProperty("VALID_MODULE_PERMISSIONS");
        });
    });
});

