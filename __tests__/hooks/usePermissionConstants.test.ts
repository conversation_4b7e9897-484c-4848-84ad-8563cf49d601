/* eslint-disable @typescript-eslint/no-explicit-any */
import { renderHook, act } from "@testing-library/react";
import { useAppSelector } from "@/redux/hooks";
import usePermissionConstants, { useCommonPermissionGroups } from "@/hooks/usePermissionConstants";
import type { Permission } from "@/redux/types/permissions";

// Mock Redux hooks
jest.mock("@/redux/hooks", () => ({
    useAppSelector: jest.fn(),
}));

const mockUseAppSelector = useAppSelector as jest.MockedFunction<typeof useAppSelector>;

describe("usePermissionConstants", () => {
    const mockSystemPermissions: Permission[] = [
        {
            id: 1, name: "View all transactions (CIB + external channels)", appModule: "transactions",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 2, name: "View transaction details", appModule: "transactions",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 3, name: "Generate transaction receipts", appModule: "transactions",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 4, name: "View team members", appModule: "team",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 5, name: "Create custom roles", appModule: "team",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 6, name: "View and edit roles", appModule: "team",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 7, name: "Send money to beneficiaries", appModule: "transfers",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 8, name: "View own accounts", appModule: "accounts",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 9, name: "View dashboard", appModule: "dashboard",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 10, name: "View reports", appModule: "reports",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
    ];

    const mockReduxState = {
        permissions: {
            systemPermissions: mockSystemPermissions,
            isReady: true,
            isInitialized: true,
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseAppSelector.mockImplementation((selector: any) => selector(mockReduxState));
    });

    describe("Basic functionality", () => {
        it("should return permission constants when permissions are loaded", () => {
            const { result } = renderHook(() => usePermissionConstants());

            expect(result.current.isLoaded).toBe(true);
            expect(result.current.isLoading).toBe(false);
            expect(result.current.systemPermissions).toEqual(mockSystemPermissions);
            expect(result.current.PERMISSIONS).toBeDefined();
        });

        it("should handle loading state when permissions are not ready", () => {
            mockUseAppSelector.mockImplementation((selector: any) => 
                selector({
                    permissions: {
                        systemPermissions: [],
                        isReady: false,
                        isInitialized: false,
                    },
                })
            );

            const { result } = renderHook(() => usePermissionConstants());

            expect(result.current.isLoaded).toBe(false);
            expect(result.current.isLoading).toBe(true);
            expect(result.current.systemPermissions).toEqual([]);
        });
    });

    describe("getPermission function", () => {
        it("should return existing permission name", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            const permission = result.current.getPermission("View team members");
            expect(permission).toBe("View team members");
        });

        it("should return fallback when permission does not exist", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            const permission = result.current.getPermission("Non-existent permission", "fallback");
            expect(permission).toBe("fallback");
        });

        it("should return expected name when permission does not exist and no fallback provided", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            const permission = result.current.getPermission("Non-existent permission");
            expect(permission).toBe("Non-existent permission");
        });

        it("should return expected name optimistically when not loaded", () => {
            mockUseAppSelector.mockImplementation((selector: any) => 
                selector({
                    permissions: {
                        systemPermissions: [],
                        isReady: false,
                        isInitialized: false,
                    },
                })
            );

            const { result } = renderHook(() => usePermissionConstants());
            
            const permission = result.current.getPermission("Any permission");
            expect(permission).toBe("Any permission");
        });

        it("should warn in development when permission not found", () => {
            const originalEnv = { ...process.env };
            (process.env as any).NODE_ENV = "development";
            const consoleSpy = jest.spyOn(console, "warn").mockImplementation();

            const { result } = renderHook(() => usePermissionConstants());
            
            result.current.getPermission("Non-existent permission", "fallback");
            
            expect(consoleSpy).toHaveBeenCalledWith(
                "Permission \"Non-existent permission\" not found in system permissions.",
                "Using fallback: \"fallback\""
            );

            consoleSpy.mockRestore();
            process.env = originalEnv;
        });
    });

    describe("Permission constants structure", () => {
        it("should generate correct TRANSACTIONS permissions", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            expect(result.current.PERMISSIONS.TRANSACTIONS.VIEW_ALL).toBe("View all transactions (CIB + external channels)");
            expect(result.current.PERMISSIONS.TRANSACTIONS.VIEW_DETAILS).toBe("View transaction details");
            expect(result.current.PERMISSIONS.TRANSACTIONS.EXPORT).toBe("Generate transaction receipts");
        });

        it("should generate correct TEAM_MANAGEMENT permissions", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            expect(result.current.PERMISSIONS.TEAM_MANAGEMENT.VIEW_TEAM_MEMBERS).toBe("View team members");
            expect(result.current.PERMISSIONS.TEAM_MANAGEMENT.CREATE_ROLES).toBe("Create custom roles");
            expect(result.current.PERMISSIONS.TEAM_MANAGEMENT.VIEW_ROLES).toBe("View and edit roles");
        });

        it("should generate correct TRANSFERS permissions", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            expect(result.current.PERMISSIONS.TRANSFERS.SEND_MONEY).toBe("Send money to beneficiaries");
        });
    });

    describe("Utility functions", () => {
        it("should check if permission exists", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            expect(result.current.permissionExists("View team members")).toBe(true);
            expect(result.current.permissionExists("Non-existent permission")).toBe(false);
        });

        it("should return optimistic result when not loaded", () => {
            mockUseAppSelector.mockImplementation((selector: any) => 
                selector({
                    permissions: {
                        systemPermissions: [],
                        isReady: false,
                        isInitialized: false,
                    },
                })
            );

            const { result } = renderHook(() => usePermissionConstants());
            
            expect(result.current.permissionExists("Any permission")).toBe(true);
        });

        it("should get permissions by module", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            const transactionPermissions = result.current.getPermissionsByModule("transactions");
            expect(transactionPermissions).toHaveLength(3);
            expect(transactionPermissions[0].name).toBe("View all transactions (CIB + external channels)");
        });

        it("should validate permissions correctly", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            const validation = result.current.validatePermissions([
                "View team members",
                "Non-existent permission",
                "View transaction details"
            ]);
            
            expect(validation.valid).toEqual(["View team members", "View transaction details"]);
            expect(validation.invalid).toEqual(["Non-existent permission"]);
        });

        it("should return optimistic validation when not loaded", () => {
            mockUseAppSelector.mockImplementation((selector: any) => 
                selector({
                    permissions: {
                        systemPermissions: [],
                        isReady: false,
                        isInitialized: false,
                    },
                })
            );

            const { result } = renderHook(() => usePermissionConstants());
            
            const validation = result.current.validatePermissions(["Any permission"]);
            expect(validation.valid).toEqual(["Any permission"]);
            expect(validation.invalid).toEqual([]);
        });

        it("should get all permission names", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            const allNames = result.current.getAllPermissionNames();
            expect(allNames).toHaveLength(10);
            expect(allNames).toContain("View team members");
        });

        it("should get permission count", () => {
            const { result } = renderHook(() => usePermissionConstants());
            
            expect(result.current.getPermissionCount()).toBe(10);
        });
    });

    describe("Memoization", () => {
        it("should memoize permission constants", () => {
            const { result, rerender } = renderHook(() => usePermissionConstants());
            
            const firstPermissions = result.current.PERMISSIONS;
            rerender();
            const secondPermissions = result.current.PERMISSIONS;
            
            expect(firstPermissions).toBe(secondPermissions);
        });

        it("should update when system permissions change", () => {
            const { result, rerender } = renderHook(() => usePermissionConstants());
            
            const firstPermissions = result.current.PERMISSIONS;
            
            // Change the mock data
            mockUseAppSelector.mockImplementation((selector: any) => 
                selector({
                    permissions: {
                        systemPermissions: [...mockSystemPermissions, { id: 11, name: "New permission", appModule: "new" }],
                        isReady: true,
                        isInitialized: true,
                    },
                })
            );
            
            rerender();
            const secondPermissions = result.current.PERMISSIONS;
            
            expect(firstPermissions).not.toBe(secondPermissions);
        });
    });
});

describe("useCommonPermissionGroups", () => {
    const mockSystemPermissions: Permission[] = [
        {
            id: 1, name: "View all transactions (CIB + external channels)", appModule: "transactions",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 2, name: "View team members", appModule: "team",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 3, name: "View dashboard", appModule: "dashboard",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
        {
            id: 4, name: "View reports", appModule: "reports",
            endpoint: "",
            method: "",
            createdDate: null,
            createdBy: null,
            lastModifiedDate: null,
            lastModifiedBy: null
        },
    ];

    beforeEach(() => {
        mockUseAppSelector.mockImplementation((selector: any) => 
            selector({
                permissions: {
                    systemPermissions: mockSystemPermissions,
                    isReady: true,
                    isInitialized: true,
                },
            })
        );
    });

    it("should return common permission groups", () => {
        const { result } = renderHook(() => useCommonPermissionGroups());
        
        expect(result.current.ADMIN).toBeDefined();
        expect(result.current.MANAGER).toBeDefined();
        expect(result.current.USER).toBeDefined();
        expect(result.current.READ_ONLY).toBeDefined();
    });

    it("should include correct permissions in MANAGER group", () => {
        const { result } = renderHook(() => useCommonPermissionGroups());
        
        expect(result.current.MANAGER).toContain("View all transactions (CIB + external channels)");
        expect(result.current.MANAGER).toContain("View team members");
    });
});
