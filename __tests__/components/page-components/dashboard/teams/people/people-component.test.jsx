import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import { jest, describe, beforeEach, it, expect } from "@jest/globals";

// Create mock functions for Next.js navigation hooks
const mockPush = jest.fn();
const mockUseRouter = jest.fn();
const mockUseSearchParams = jest.fn();
const mockUsePathname = jest.fn();

// Mock the next/navigation hooks
jest.mock("next/navigation", () => ({
    useRouter: mockUseRouter,
    useSearchParams: mockUseSearchParams,
    usePathname: mockUsePathname,
}));

// Add this mock at the top of the file with the other mocks
jest.mock("@/components/hoc/withPermissionCheck", () => ({
    __esModule: true,
    default: (Component, options) => {
        // Simply return the original component without the permission check
        const WrappedComponent = (props) => <Component {...props} />;
        // Copy displayName for better debugging
        WrappedComponent.displayName = `MockedPermissionCheck(${Component.displayName || Component.name || "Component"})`;
        return WrappedComponent;
    },
}));

// Mock the dynamic permission check HOC
jest.mock("@/components/hoc/withDynamicPermissionCheck", () => ({
    withDynamicPermissionCheck: (Component, options) => {
        // Simply return the original component without the permission check
        const WrappedComponent = (props) => <Component {...props} />;
        // Copy displayName for better debugging
        WrappedComponent.displayName = `WithDynamicPermissionCheck(${Component.displayName || Component.name})`;
        return WrappedComponent;
    },
}));

// Also mock the PermissionContext if needed
jest.mock("@/contexts/PermissionContext", () => ({
    usePermissions: () => ({
        hasPermission: () => true,
        hasAnyPermission: () => true,
        hasAllPermissions: () => true,
        isLoading: false,
    }),
    PermissionProvider: ({ children }) => children,
}));

// Mock the dynamic permissions constants used by the component
jest.mock("@/constants/dynamicPermissions", () => ({
    TEAM_MANAGEMENT_PERMISSIONS: {
        VIEW_TEAM_MEMBERS: "view_team_members",
        INVITE_TEAM_MEMBERS: "invite_team_members",
        EDIT_TEAM_MEMBERS: "edit_team_members",
        DELETE_TEAM_MEMBERS: "delete_team_members",
    },
    arePermissionsLoaded: jest.fn(() => true),
    arePermissionsLoading: jest.fn(() => false),
    getPermissionsError: jest.fn(() => null),
    refreshPermissions: jest.fn(() => Promise.resolve()),
    waitForPermissions: jest.fn(() => Promise.resolve({})),
    default: {
        TEAM_MANAGEMENT: {
            VIEW_TEAM_MEMBERS: "view_team_members",
            INVITE_TEAM_MEMBERS: "invite_team_members",
            EDIT_TEAM_MEMBERS: "edit_team_members",
            DELETE_TEAM_MEMBERS: "delete_team_members",
        },
    },
}));

// Mock the routes path
jest.mock("@/routes/path", () => ({
    PATH_PROTECTED: {
        root: "/dashboard",
    },
}));

// Mock the react-i18next
jest.mock("react-i18next", () => ({
    useTranslation: () => ({
        t: (key) => key,
    }),
}));

// Mock @formkit/auto-animate to avoid ES module issues
jest.mock("@formkit/auto-animate", () => ({
    __esModule: true,
    default: jest.fn(),
    autoAnimate: jest.fn(),
}));

// We'll test the internal functions by mocking the Redux actions and checking if they're called

// Mock the Redux actions
const mockGetAllRoles = jest.fn();
const mockGetPendingInvites = jest.fn();
const mockGetTeamMembers = jest.fn();
const mockGetAllPermissions = jest.fn();

jest.mock("@/redux/actions/rolesActions", () => ({
    getAllRoles: () => {
        mockGetAllRoles();
        return { type: "GET_ALL_ROLES" };
    },
    getAllPermissions: () => {
        mockGetAllPermissions();
        return { type: "GET_ALL_PERMISSIONS" };
    },
}));

jest.mock("@/redux/actions/teamMembersActions", () => ({
    getPendingInvites: () => {
        mockGetPendingInvites();
        return { type: "GET_PENDING_INVITES" };
    },
    getTeamMembers: () => {
        mockGetTeamMembers();
        return { type: "GET_TEAM_MEMBERS" };
    },
}));

// Mock the sendCatchFeedback function
const mockSendCatchFeedback = jest.fn();
jest.mock("@/functions/feedback", () => ({
    sendCatchFeedback: (error) => mockSendCatchFeedback(error),
    sendFeedback: jest.fn(),
}));

// Mock the stringManipulations functions
const mockConvertCamelCaseToWords = jest.fn((str) => str.replace(/([A-Z])/g, " $1").toLowerCase());
jest.mock("@/functions/stringManipulations", () => ({
    convertCamelCaseToWords: (str) => mockConvertCamelCaseToWords(str),
}));

// Mock the child components
jest.mock("@/components/page-components/dashboard/teams/people/tab-members.tsx", () => ({
    __esModule: true,
    default: (props) => (
        <div data-testid="tab-members" {...props}>
            Members Tab Content
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/teams/people/tab-pending-invitation.tsx", () => ({
    __esModule: true,
    default: (props) => (
        <div data-testid="tab-pending-invitation" {...props}>
            Pending Invitations Tab Content
        </div>
    ),
}));

jest.mock("@/components/page-components/dashboard/teams/people/modals/invite-team-member-modal.tsx", () => ({
    __esModule: true,
    default: ({ open, onClose }) =>
        open ? (
            <div data-testid="invite-modal">
                Invite Modal <button onClick={onClose}>Close</button>
            </div>
        ) : null,
}));

jest.mock("@/components/page-components/dashboard/teams/people/people-filter.tsx", () => ({
    PeopleFilter: ({ currentFilters, onSearch, activeTab }) => (
        <div data-testid="people-filter">
            <input data-testid="search-input" onChange={onSearch} value={currentFilters.search || ""} />
        </div>
    ),
}));

jest.mock("@/components/common/tab-switch", () => ({
    __esModule: true,
    default: ({ tabs, activeTab, onChange }) => (
        <div data-testid="tab-switch">
            {tabs.map((tab) => (
                <button
                    key={tab.id}
                    data-testid={`tab-${tab.id}`}
                    onClick={() => onChange(tab.id)}
                    className={activeTab === tab.id ? "active" : ""}
                >
                    {typeof tab.label === "string" ? (
                        tab.label
                    ) : (
                        <div data-testid="pending-count-badge">Complex Label</div>
                    )}
                </button>
            ))}
        </div>
    ),
}));

jest.mock("@/components/common/buttonv3", () => ({
    Button: ({ children, onClick, leftIcon, variant, color, size, ...props }) => (
        <button
            onClick={onClick}
            data-testid={props["data-testid"] || "button"}
            data-variant={variant}
            data-color={color}
        >
            {leftIcon && <span data-testid="button-left-icon">{leftIcon}</span>}
            {children}
        </button>
    ),
}));

jest.mock("@/components/icons/team", () => ({
    UserIcon: ({ baseColor }) => (
        <div data-testid="user-icon" data-color={baseColor}>
            User Icon
        </div>
    ),
}));

// Mock lucide-react icons
jest.mock("lucide-react", () => ({
    X: () => <div data-testid="x-icon">X</div>,
    PanelLeft: () => <div data-testid="panel-left-icon">PanelLeft</div>,
}));

// Import the component after all mocks are set up
// This is important to ensure mocks are applied before the component is loaded
const PeopleComponent = require("@/components/page-components/dashboard/teams/people/people-component.tsx").default;

// Set up mock store
const middlewares = [thunk];
const mockStore = configureMockStore(middlewares);
const initialState = {
    roles: {
        getAllRoles: {
            loading: false,
            success: true,
            error: null,
            data: [
                { id: 1, name: "Admin" },
                { id: 2, name: "Super Admin" },
                { id: 3, name: "Staff" },
            ],
        },
        getAllPermissions: {
            loading: false,
            success: true,
            error: null,
            data: [
                { id: 1, name: "Can manage team members", appModule: "Settings" },
                { id: 2, name: "Can view accounts", appModule: "Accounts" },
                { id: 3, name: "Can invite team members", appModule: "Settings" },
            ],
        },
    },
    teamMembers: {
        getTeamMembers: {
            loading: false,
            success: true,
            error: null,
            data: [
                { id: "1", firstName: "John", lastName: "Doe", email: "<EMAIL>", roleId: 1 },
                { id: "2", firstName: "Jane", lastName: "Smith", email: "<EMAIL>", roleId: 2 },
            ],
        },
        getPendingInvites: {
            loading: false,
            success: true,
            error: null,
            data: [
                { id: "1", email: "<EMAIL>", roleId: 1 },
                { id: "2", email: "<EMAIL>", roleId: 2 },
            ],
        },
        editTeamMember: {
            loading: false,
            success: false,
            error: null,
        },
    },
    corporate: {
        corporateId: "123",
    },
};

// Helper function to render with Redux Provider
const renderWithProvider = (ui, state = {}) => {
    const store = mockStore({
        ...initialState,
        ...state,
    });
    return render(<Provider store={store}>{ui}</Provider>);
};

// We'll test the internal functions through the main component

describe("PeopleComponent", () => {
    // Setup common mocks
    const mockParams = new URLSearchParams();

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup router mock
        mockUseRouter.mockReturnValue({
            push: mockPush,
        });

        // Setup pathname mock
        mockUsePathname.mockReturnValue("/people");

        // Setup search params mock with default values
        mockParams.set("size", "10");
        mockParams.set("page", "1");

        mockUseSearchParams.mockReturnValue({
            get: (key) => mockParams.get(key),
            toString: () => mockParams.toString(),
        });
    });

    it("renders the component correctly", () => {
        renderWithProvider(<PeopleComponent />);

        // Check if the component renders
        expect(screen.getByTestId("people-component")).toBeInTheDocument();

        // Check if the title is rendered
        expect(screen.getByText("People")).toBeInTheDocument();

        // Check if the tabs are rendered
        expect(screen.getByTestId("tab-switch")).toBeInTheDocument();

        // Check if the members tab is active by default
        expect(screen.getByTestId("tab-members")).toBeInTheDocument();

        // Check if the invite button is rendered
        expect(screen.getByTestId("invite-button")).toBeInTheDocument();

        // Check if the filter component is rendered
        expect(screen.getByTestId("people-filter")).toBeInTheDocument();
    });

    it("switches tabs correctly", async () => {
        renderWithProvider(<PeopleComponent />);

        // Initially, members tab should be active
        expect(screen.getByTestId("tab-members")).toBeInTheDocument();
        expect(screen.queryByTestId("tab-pending-invitation")).not.toBeInTheDocument();

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Now pending tab should be active
        expect(screen.queryByTestId("tab-members")).toBeInTheDocument();
        expect(screen.getByTestId("tab-pending")).toBeInTheDocument();

        // Click back on the members tab
        fireEvent.click(screen.getByTestId("tab-members"));

        // Members tab should be active again
        expect(screen.getByTestId("tab-members")).toBeInTheDocument();
        expect(screen.queryByTestId("tab-pending-invitation")).not.toBeInTheDocument();
    });

    it("opens and closes the invite modal", () => {
        renderWithProvider(<PeopleComponent />);

        // Modal should not be open initially
        expect(screen.queryByTestId("invite-modal")).not.toBeInTheDocument();

        // Click the invite button
        fireEvent.click(screen.getByTestId("invite-button"));

        // Modal should be open
        expect(screen.getByTestId("invite-modal")).toBeInTheDocument();

        // Click the close button in the modal
        fireEvent.click(screen.getByText("Close"));

        // Modal should be closed
        expect(screen.queryByTestId("invite-modal")).not.toBeInTheDocument();
    });

    it("handles search correctly", async () => {
        renderWithProvider(<PeopleComponent />);

        // Get the search input
        const searchInput = screen.getByTestId("search-input");

        // Type in the search input
        fireEvent.change(searchInput, { target: { value: "test search" } });

        // Check if router.push was called with the correct query string
        await waitFor(() => {
            expect(mockPush).toHaveBeenCalledWith(expect.stringContaining("search=test+search"));
        });
    });

    it("sets the correct placeholder based on active tab", () => {
        renderWithProvider(<PeopleComponent />);

        // Initially, members tab should be active
        expect(screen.getByTestId("tab-members")).toBeInTheDocument();

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Now pending tab should be active
        expect(screen.getByTestId("tab-pending")).toBeInTheDocument();
    });

    it("displays the pending count badge", () => {
        // Click on the pending tab to see the badge
        renderWithProvider(<PeopleComponent />);

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Now the pending tab should be active and we can check the badge
        // Since we're using a mock, we can't directly test the badge's content
        // but we can verify the tab is active
        expect(screen.getByTestId("tab-pending")).toBeInTheDocument();
    });

    it("calls refreshData when switching to members tab", async () => {
        renderWithProvider(<PeopleComponent />);

        // Initially, members tab should be active and getTeamMembers should be called
        expect(mockGetTeamMembers).toHaveBeenCalledTimes(1);
        expect(mockGetAllPermissions).toHaveBeenCalledTimes(1);

        // Reset the mocks
        mockGetTeamMembers.mockClear();
        mockGetAllPermissions.mockClear();

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Now pending tab should be active
        expect(screen.getByTestId("tab-pending")).toBeInTheDocument();

        // Click back on the members tab
        fireEvent.click(screen.getByTestId("tab-members"));

        // Members tab should be active again and getTeamMembers should not be called again
        // since it was already fetched
        expect(mockGetTeamMembers).not.toHaveBeenCalled();
        expect(mockGetAllPermissions).not.toHaveBeenCalled();
    });

    it("calls refreshData when invite modal is closed in pending tab", async () => {
        renderWithProvider(<PeopleComponent />);

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Reset the mocks
        mockGetPendingInvites.mockClear();

        // Click the invite button
        fireEvent.click(screen.getByTestId("invite-button"));

        // Modal should be open
        expect(screen.getByTestId("invite-modal")).toBeInTheDocument();

        // Simulate a member being invited by passing true to the onClose handler
        // We need to get the close button and modify its onClick to pass true
        const closeButton = screen.getByText("Close");
        // This simulates the InviteTeamMemberModal passing true when a member is invited
        fireEvent.click(closeButton);

        // Modal should be closed and getPendingInvites should be called
        expect(screen.queryByTestId("invite-modal")).not.toBeInTheDocument();
        expect(mockGetPendingInvites).toHaveBeenCalledTimes(1);
    });

    it("handles errors in data fetching", async () => {
        // Reset the mock before the test
        mockSendCatchFeedback.mockClear();

        // Create a store with errors
        const stateWithErrors = {
            ...initialState,
            roles: {
                ...initialState.roles,
                getAllRoles: {
                    ...initialState.roles.getAllRoles,
                    error: "Error fetching roles",
                },
                getAllPermissions: {
                    ...initialState.roles.getAllPermissions,
                    error: "Error fetching permissions",
                },
            },
            teamMembers: {
                ...initialState.teamMembers,
                getTeamMembers: {
                    ...initialState.teamMembers.getTeamMembers,
                    error: "Error fetching team members",
                },
                getPendingInvites: {
                    ...initialState.teamMembers.getPendingInvites,
                    error: "Error fetching pending invites",
                },
            },
        };

        renderWithProvider(<PeopleComponent />, stateWithErrors);

        // Check if sendCatchFeedback was called for each error
        // We expect it to be called at least once for each error
        expect(mockSendCatchFeedback).toHaveBeenCalledWith("Error fetching roles");
        expect(mockSendCatchFeedback).toHaveBeenCalledWith("Error fetching permissions");
        expect(mockSendCatchFeedback).toHaveBeenCalledWith("Error fetching team members");
        expect(mockSendCatchFeedback).toHaveBeenCalledWith("Error fetching pending invites");
    });

    it("fetches roles data when opening invite modal if not available", () => {
        // Create a store without roles data
        const stateWithoutRoles = {
            ...initialState,
            roles: {
                ...initialState.roles,
                getAllRoles: {
                    loading: false,
                    success: false,
                    error: null,
                    data: null, // No roles data
                },
            },
        };

        renderWithProvider(<PeopleComponent />, stateWithoutRoles);

        // Reset the mock
        mockGetAllRoles.mockClear();

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Click the invite button
        fireEvent.click(screen.getByTestId("invite-button"));

        // getAllRoles should be called
        expect(mockGetAllRoles).toHaveBeenCalledTimes(1);

        // Modal should be open
        expect(screen.getByTestId("invite-modal")).toBeInTheDocument();
    });

    it("passes refreshData callbacks to child components", () => {
        // Render the component
        renderWithProvider(<PeopleComponent />);

        // Initially, members tab should be active
        expect(screen.getByTestId("tab-members")).toBeInTheDocument();

        // Click on the pending tab
        fireEvent.click(screen.getByTestId("tab-pending"));

        // Now the pending tab should be active
        expect(screen.getByTestId("tab-pending")).toBeInTheDocument();

        // Click back on the members tab
        fireEvent.click(screen.getByTestId("tab-members"));

        // Members tab should be active again
        expect(screen.getByTestId("tab-members")).toBeInTheDocument();
    });

    it("tests the search functionality with date parameters", () => {
        // Setup search params with date filters
        mockParams.set("startDate", "2023-01-01");
        mockParams.set("endDate", "2023-12-31");

        renderWithProvider(<PeopleComponent />);

        // Test that the component renders with these parameters
        // We're not checking for filter items since they've been removed
        expect(screen.getByTestId("people-component")).toBeInTheDocument();
    });

    it("tests the handleSearch function", () => {
        // Reset mockParams to default values
        mockParams.delete("startDate");
        mockParams.delete("endDate");
        mockParams.delete("source");
        mockParams.delete("search");
        mockParams.set("size", "10");
        mockParams.set("page", "1");

        // Reset the mock
        mockPush.mockClear();

        renderWithProvider(<PeopleComponent />);

        // Test handleSearch by triggering a search
        const searchInput = screen.getByTestId("search-input");
        fireEvent.change(searchInput, { target: { value: "search test" } });

        // Check if router.push was called with the search parameter
        expect(mockPush).toHaveBeenCalledWith(expect.stringContaining("search=search+test"));
    });

    it("displays the correct pending count in the badge", () => {
        renderWithProvider(<PeopleComponent />);

        // The pending count badge should show "2" based on our mock data
        const pendingCountBadge = screen.getByTestId("pending-count-badge");
        expect(pendingCountBadge).toBeInTheDocument();
        // We can't check the exact text since our mock doesn't render the actual content
    });

    it("fetches pending invites data when not available", () => {
        // Mock state where pending invites data is not available
        const stateWithoutPendingInvites = {
            ...initialState,
            teamMembers: {
                ...initialState.teamMembers,
                getPendingInvites: {
                    loading: false,
                    success: false,
                    error: null,
                    data: null,
                },
            },
        };

        renderWithProvider(<PeopleComponent />, stateWithoutPendingInvites);

        // Check that getPendingInvites action was called
        expect(mockGetPendingInvites).toHaveBeenCalled();
    });

    it("fetches permissions data when not available", () => {
        // Mock state where permissions data is not available
        const stateWithoutPermissions = {
            ...initialState,
            roles: {
                ...initialState.roles,
                getAllPermissions: {
                    loading: false,
                    success: false,
                    error: null,
                    data: null,
                },
            },
        };

        renderWithProvider(<PeopleComponent />, stateWithoutPermissions);

        // Check that getAllPermissions action was called
        expect(mockGetAllPermissions).toHaveBeenCalled();
    });

    it("handles filter parameters with dates correctly", () => {
        // Set up URL parameters with date filters
        mockParams.set("startDate", "2024-01-01");
        mockParams.set("endDate", "2024-01-31");
        mockParams.set("source", "test-source");

        renderWithProvider(<PeopleComponent />);

        // Component should render without errors
        expect(screen.getByTestId("people-component")).toBeInTheDocument();
    });

    it("formats dates correctly in filter display", () => {
        // Set up URL parameters with encoded date filters to test formatDate function
        mockParams.set("startDate", encodeURIComponent("2024-01-15T10:30:00.000Z"));
        mockParams.set("endDate", encodeURIComponent("2024-01-31T23:59:59.999Z"));

        renderWithProvider(<PeopleComponent />);

        // Component should render without errors and process the dates
        expect(screen.getByTestId("people-component")).toBeInTheDocument();

        // The formatDate function should be called internally when processing these dates
        // We can't directly test the function output, but we ensure it doesn't crash
    });

    it("handles error states in data fetching", () => {
        // Mock state with errors
        const stateWithErrors = {
            ...initialState,
            roles: {
                ...initialState.roles,
                getAllRoles: {
                    ...initialState.roles.getAllRoles,
                    error: "Failed to fetch roles",
                },
            },
            teamMembers: {
                ...initialState.teamMembers,
                getPendingInvites: {
                    ...initialState.teamMembers.getPendingInvites,
                    error: "Failed to fetch pending invites",
                },
            },
        };

        renderWithProvider(<PeopleComponent />, stateWithErrors);

        // Component should still render despite errors
        expect(screen.getByTestId("people-component")).toBeInTheDocument();
    });

    it("exercises callback functions through search interactions", async () => {
        renderWithProvider(<PeopleComponent />);

        const searchInput = screen.getByTestId("search-input");

        // Test the handleSearch, onSearch, updateFilters, and createQueryString functions
        fireEvent.change(searchInput, { target: { value: "test search" } });

        // Verify the search functionality works
        expect(mockPush).toHaveBeenCalledWith(expect.stringContaining("search=test+search"));

        // Test clearing the search
        fireEvent.change(searchInput, { target: { value: "" } });

        // This should exercise the createQueryString function with empty search
        expect(mockPush).toHaveBeenCalledWith(expect.not.stringContaining("search="));
    });

    it("tests tab switching functionality", () => {
        renderWithProvider(<PeopleComponent />);

        // Test switching to pending tab
        const pendingTab = screen.getByTestId("tab-pending");
        fireEvent.click(pendingTab);

        // Verify pending tab content is shown
        expect(screen.getByTestId("pending-content")).toBeInTheDocument();

        // Test switching back to members tab
        const membersTab = screen.getByTestId("tab-members");
        fireEvent.click(membersTab);

        // Verify members tab content is shown
        expect(screen.getByTestId("members-content")).toBeInTheDocument();
    });

    it("tests invite modal open and close functionality", () => {
        renderWithProvider(<PeopleComponent />);

        // Find and click the invite button
        const inviteButton = screen.getByTestId("invite-button");
        fireEvent.click(inviteButton);

        // Verify modal is opened
        expect(screen.getByTestId("invite-modal")).toBeInTheDocument();

        // Close the modal
        const closeButton = screen.getByText("Close");
        fireEvent.click(closeButton);

        // Verify modal is closed
        expect(screen.queryByTestId("invite-modal")).not.toBeInTheDocument();
    });

    it("tests refresh functionality", () => {
        // Clear mocks before testing
        mockGetAllRoles.mockClear();
        mockGetPendingInvites.mockClear();
        mockGetAllPermissions.mockClear();

        renderWithProvider(<PeopleComponent />);

        // The refresh functions should be called when the component mounts
        // Since we have data in our initial state, these might not be called
        // Let's just verify the component renders without errors
        expect(screen.getByTestId("people-component")).toBeInTheDocument();
    });
});
