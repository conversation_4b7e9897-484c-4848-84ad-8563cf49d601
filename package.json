{"name": "rova-cba-cib-client-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "NODE_OPTIONS='--max_old_space_size=16384 --expose-gc' jest --maxWorkers=1", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:changed": "jest --coverage --only<PERSON><PERSON>ed", "lint:fix": "eslint --fix \"**/*.{ts,tsx}\"", "prettier:fix": "prettier --write .", "type:check": "tsc --project tsconfig.json --noEmit", "prepare": "husky"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@peculiar/webcrypto": "^1.5.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-id": "^1.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@reduxjs/toolkit": "^1.9.1", "@tanstack/react-table": "^8.20.5", "@types/uuid": "^10.0.0", "axios": "^1.7.7", "axios-mock-adapter": "^2.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "export-to-csv": "^1.4.0", "formik": "^2.4.6", "framer-motion": "^11.5.4", "i18next": "^23.16.4", "i18next-browser-languagedetector": "^8.0.0", "jest-axe": "^9.0.0", "jest-next-dynamic": "^1.0.2", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "lucide-react": "^0.468.0", "next": "14.2.10", "nuqs": "^2.2.3", "papaparse": "^5.5.2", "pluralize": "^8.0.0", "radix-ui": "^1.1.2", "react": "^18", "react-click-away-listener": "^2.2.3", "react-day-picker": "^9.4.1", "react-dom": "^18", "react-i18next": "^15.1.0", "react-indiana-drag-scroll": "^2.2.0", "react-modal": "^3.16.1", "react-otp-input": "^3.1.1", "react-qr-code": "^2.0.15", "react-redux": "^8.0.5", "react-select": "^5.8.2", "react-table-plugins": "^1.3.4", "react-toastify": "^10.0.5", "reactjs-popup": "^2.0.6", "recharts": "^2.15.0", "redux-mock-store": "^1.5.5", "redux-persist": "^6.0.0", "sharp": "^0.34.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "uuid": "^11.0.5", "xlsx": "^0.18.5", "yup": "^1.4.0", "yup-password": "^0.4.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@next/eslint-plugin-next": "^15.0.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/jspdf": "^1.3.3", "@types/jwt-decode": "^2.2.1", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/papaparse": "^5.3.15", "@types/pluralize": "^0.0.33", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/react-select": "^5.0.1", "@types/redux-mock-store": "^1.5.0", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "babel-jest": "^29.7.0", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-next": "^15.0.3", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-stare": "^2.5.2", "lint-staged": "^15.2.10", "postcss": "^8", "prettier": "3.4.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.5", "typescript": "^5"}}